const puppeteer = require("puppeteer");
const readline = require("readline");

function askQuestion(query) {
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  });
  return new Promise(resolve => rl.question(query, ans => {
    rl.close();
    resolve(ans);
  }))
}

(async () => {
  try {
    console.log("Iniciando o script...");
    console.log("Abrindo o navegador...");

    const browser = await puppeteer.launch({
      headless: false, // precisa ser "false" para permitir clicar nos botões
      args: ["--kiosk-printing", "--no-sandbox", "--disable-setuid-sandbox"], // impressão silenciosa
      // Removendo executablePath para usar o Chrome padrão do Puppeteer
    });

  const page = await browser.newPage();
  console.log("Nova página criada.");

  console.log("Navegando para a página de login...");
  await page.goto("https://doity.com.br/admin/users/login");
  console.log("Página de login carregada.");

  console.log("Preenchendo credenciais...");
  await page.type("#UserUsername", "<EMAIL>");
  await page.type("#UserPassword", "Davicomunic22#");

  console.log("Clicando no botão de login...");
  await page.click('input[value="Entrar"]');

  console.log("Aguardando navegação após login...");
  await page.waitForNavigation();
  console.log("Login realizado com sucesso!");

  let currentPage = await askQuestion("Digite o número da página para começar: ");
  currentPage = parseInt(currentPage);
  if (isNaN(currentPage) || currentPage < 1) currentPage = 1;

  let hasNext = true;

  while (hasNext) {

    // Navegar para a página atual
    console.log(`Navegando para a página ${currentPage}...`);
    try {
      await page.goto(`https://doity.com.br/admin/credenciamento/index/263089/page:${currentPage}`, {
        waitUntil: "networkidle0",
      });
      console.log(`Página ${currentPage} carregada com sucesso.`);
    } catch (error) {
      console.log(`Erro ao carregar página ${currentPage}:`, error.message);
      console.log("Tentando continuar...");
      await page.goto(`https://doity.com.br/admin/credenciamento/index/263089/page:${currentPage}`, {
        waitUntil: "domcontentloaded",
        timeout: 30000
      });
    }

    // ---- LOOP DE IMPRESSÃO ----
    console.log("Procurando botões de impressão...");
    const printButtons = await page.$$("#bt-imprimir-etiqueta");
    console.log(`Encontrados ${printButtons.length} botões de impressão.`);

    if (printButtons.length === 0) {
      console.log("Nenhum botão de impressão encontrado nesta página.");
    } else {
      for (let i = 0; i < printButtons.length; i++) {
        const currentButtons = await page.$$("#bt-imprimir-etiqueta");
        const button = currentButtons[i];
        if (!button) {
          console.log(`Botão ${i + 1} não encontrado, pulando...`);
          continue;
        }

        console.log(`Página ${currentPage} - Imprimindo inscrito ${i + 1} de ${printButtons.length}`);
        try {
          await page.evaluate((btn) => btn.click(), button);
          await new Promise(resolve => setTimeout(resolve, 5000));
          console.log(`Inscrito ${i + 1} processado com sucesso.`);
        } catch (error) {
          console.log(`Erro ao processar inscrito ${i + 1}:`, error.message);
        }
      }
    }

    // ---- PERGUNTAR AO USUÁRIO SE QUER AVANÇAR ----
    const answer = await askQuestion("Pressione ENTER para ir para a próxima página ou digite 'sair' para encerrar: ");
    if (answer.toLowerCase() === "sair") {
      hasNext = false;
    } else {
      currentPage++; // incrementar página manualmente
    }
  }

  await browser.close();
  console.log("Script finalizado com sucesso!");

  } catch (error) {
    console.error("Erro durante a execução:", error.message);
    console.error("Stack trace:", error.stack);
  }
})();
