const puppeteer = require("puppeteer");

(async () => {
  try {
    console.log("Testando Puppeteer...");
    console.log("Abrindo navegador...");
    
    const browser = await puppeteer.launch({
      headless: false,
      args: ["--no-sandbox", "--disable-setuid-sandbox"]
    });
    
    console.log("Navegador aberto com sucesso!");
    
    const page = await browser.newPage();
    console.log("Nova página criada.");
    
    await page.goto("https://www.google.com");
    console.log("Google carregado com sucesso!");
    
    // Aguardar 5 segundos para você ver
    await new Promise(resolve => setTimeout(resolve, 5000));
    
    await browser.close();
    console.log("Teste concluído com sucesso!");
    
  } catch (error) {
    console.error("Erro no teste:", error.message);
    console.error("Stack trace:", error.stack);
  }
})();
