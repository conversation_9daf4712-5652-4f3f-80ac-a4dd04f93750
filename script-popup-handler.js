const puppeteer = require("puppeteer");
const readline = require("readline");

function askQuestion(query) {
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  });
  return new Promise(resolve => rl.question(query, ans => {
    rl.close();
    resolve(ans);
  }))
}

// Função para aguardar popup de impressão
async function waitForPrintPopup(browser, page, button, inscritoNum) {
  return new Promise(async (resolve) => {
    let popupHandled = false;
    let timeout;
    
    // Listener para novas páginas (popups)
    const newPageHandler = async (newPage) => {
      if (popupHandled) return;
      popupHandled = true;
      
      console.log(`Popup de impressão detectado para inscrito ${inscritoNum}`);
      
      try {
        // Aguardar o popup carregar
        await newPage.waitForLoadState?.('load') || 
              await newPage.waitForSelector('body', { timeout: 5000 });
        
        console.log("Popup carregado, processando impressão...");
        
        // Aguardar um tempo para a impressão ser processada
        await new Promise(r => setTimeout(r, 3000));
        
        // Tentar fechar o popup
        if (!newPage.isClosed()) {
          await newPage.close();
          console.log("Popup fechado.");
        }
      } catch (error) {
        console.log("Erro ao processar popup:", error.message);
      }
      
      clearTimeout(timeout);
      browser.off('targetcreated', newPageHandler);
      resolve();
    };
    
    // Timeout de segurança
    timeout = setTimeout(() => {
      if (!popupHandled) {
        console.log(`Timeout aguardando popup para inscrito ${inscritoNum}`);
        browser.off('targetcreated', newPageHandler);
        resolve();
      }
    }, 10000);
    
    // Registrar listener
    browser.on('targetcreated', async (target) => {
      if (target.type() === 'page') {
        const newPage = await target.page();
        if (newPage) {
          newPageHandler(newPage);
        }
      }
    });
    
    // Clicar no botão
    await page.evaluate((btn) => btn.click(), button);
    console.log(`Clicou no botão de impressão do inscrito ${inscritoNum}`);
  });
}

(async () => {
  try {
    console.log("Iniciando o script...");
    console.log("Abrindo o navegador...");
    
    const browser = await puppeteer.launch({
      headless: false,
      args: ["--kiosk-printing", "--no-sandbox", "--disable-setuid-sandbox"],
    });

    const page = await browser.newPage();
    console.log("Nova página criada.");

    console.log("Navegando para a página de login...");
    await page.goto("https://doity.com.br/admin/users/login");
    console.log("Página de login carregada.");

    console.log("Preenchendo credenciais...");
    await page.type("#UserUsername", "<EMAIL>");
    await page.type("#UserPassword", "Davicomunic22#");
    
    console.log("Clicando no botão de login...");
    await page.click('input[value="Entrar"]');
    
    console.log("Aguardando navegação após login...");
    await page.waitForNavigation();
    console.log("Login realizado com sucesso!");

    let currentPage = await askQuestion("Digite o número da página para começar: ");
    currentPage = parseInt(currentPage);
    if (isNaN(currentPage) || currentPage < 1) currentPage = 1;

    let hasNext = true;

    while (hasNext) {
      console.log(`Navegando para a página ${currentPage}...`);
      try {
        await page.goto(`https://doity.com.br/admin/credenciamento/index/263089/page:${currentPage}`, { 
          waitUntil: "networkidle0",
        });
        console.log(`Página ${currentPage} carregada com sucesso.`);
      } catch (error) {
        console.log(`Erro ao carregar página ${currentPage}:`, error.message);
        console.log("Tentando continuar...");
        await page.goto(`https://doity.com.br/admin/credenciamento/index/263089/page:${currentPage}`, { 
          waitUntil: "domcontentloaded",
          timeout: 30000 
        });
      }

      console.log("Procurando botões de impressão...");
      const printButtons = await page.$$("#bt-imprimir-etiqueta");
      console.log(`Encontrados ${printButtons.length} botões de impressão.`);
      
      if (printButtons.length === 0) {
        console.log("Nenhum botão de impressão encontrado nesta página.");
      } else {
        for (let i = 0; i < printButtons.length; i++) {
          const currentButtons = await page.$$("#bt-imprimir-etiqueta");
          const button = currentButtons[i];
          if (!button) {
            console.log(`Botão ${i + 1} não encontrado, pulando...`);
            continue;
          }

          console.log(`Página ${currentPage} - Processando inscrito ${i + 1} de ${printButtons.length}`);
          try {
            await waitForPrintPopup(browser, page, button, i + 1);
            console.log(`Inscrito ${i + 1} processado com sucesso.`);
          } catch (error) {
            console.log(`Erro ao processar inscrito ${i + 1}:`, error.message);
          }
        }
      }

      const answer = await askQuestion("Pressione ENTER para ir para a próxima página ou digite 'sair' para encerrar: ");
      if (answer.toLowerCase() === "sair") {
        hasNext = false;
      } else {
        currentPage++;
      }
    }

    await browser.close();
    console.log("Script finalizado com sucesso!");
    
  } catch (error) {
    console.error("Erro durante a execução:", error.message);
    console.error("Stack trace:", error.stack);
  }
})();
